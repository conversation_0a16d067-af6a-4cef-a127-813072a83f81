<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-database.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-firestore.js"></script>
    <link rel="stylesheet" type="text/css" href="style.css" />
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bodymovin/5.7.8/lottie.min.js"></script>
</head>

<body class="bg-cover bg-center h-screen w-screen overflow-hidden bg-fixed flex items-center justify-center">
    <div id="header" class="fixed top-0 left-0 right-0 p-5 bg-blue-900 flex items-center justify-around h-12 text-white text-sm shadow-md hidden">
        <div class="header-texts flex justify-around w-full items-center">
            <h2 class="text-xs">Your Score: <span id="score">0</span></h2>
            <h2 class="text-xs">Section <span id="current-section">1</span>: <span id="section-name">Essentials</span></h2>
        </div>
    </div>

    <div id="user-form-container" class="hidden">
      <div class="form-container">
        <h2>Please fill out your information below</h2>
        <form id="user-form">
          <!-- First Name and Last Name form row -->
          <div class="form-row">
            <div class="form-group">
              <input
                type="text"
                id="first-name"
                name="first-name"
                required
                class="peer"
                placeholder=" "
              >
              <label for="first-name">First Name</label>
            </div>

            <div class="form-group">
              <input
                type="text"
                id="last-name"
                name="last-name"
                required
                class="peer"
                placeholder=" "
              >
              <label for="last-name">Last Name</label>
            </div>
          </div>

         <!-- Email and Phone form row -->
      <div class="form-row">
        <div class="form-group">
          <input
            type="email"
            id="email"
            name="email"
            required
            class="peer"
            placeholder=" "
          >
          <label for="email">Email</label>
          <span id="email-validation-icon"></span>
        </div>

            <div class="form-group">
              <input
                type="tel"
                id="phone"
                class="peer"
                placeholder=" "
              >
              <label for="phone">Phone Number (Optional)</label>
            </div>
          </div>

          <!-- Role form group -->
          <div class="form-group" style="position: relative;">
            <input
                type="text"
                id="role"
                name="role"
                required
                class="peer"
                placeholder=" "
            >
            <label for="role">Role e.g., HR Manager, CEO, Marketing Specialist</label>
            <span id="role-validation-icon"></span>
        </div>


          <button type="submit" id="submit-form">Submit</button>
        </form>
      </div>
    </div>

    <div id="skills-gap-analyzer-container" class="hidden">
      <!-- Framework will be initialized here -->
    </div>

    <div id="consent-container" class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50 hidden p-4">
        <div class="bg-white rounded-lg p-6 max-w-md w-full relative">
          <button id="close-consent-btn" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          <h2 class="text-xl font-bold mb-4">Before we begin!</h2>
          <p class="mb-4 text-sm md:text-base">To make your results even more helpful, we'd love some quick information about you. This will allow us to provide personalised feedback and recommendations tailored to your needs.</p>
          <p class="mb-4 text-sm md:text-base text-blue-600 font-medium">Rest assured, your information will only be used to contact you about your assessment.</p>
          <div class="mb-4">
            <input type="checkbox" id="consent-checkbox" class="mr-2">
            <label for="consent-checkbox" class="text-sm md:text-base">
              I consent to the collection and processing of my personal data as explained in the
              <a href="https://barefootelearning.com/privacy-policy-2/" target="_blank" class="text-blue-500 hover:underline">Privacy Policy</a>.
            </label>
          </div>
          <button id="consent-btn" class="w-full md:w-auto bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 text-sm md:text-base">Proceed</button>
        </div>
      </div>


    <div id="start-page" class="block text-center">
      <img src="logowhite.png" alt="SGA Logo" style="width: 200px; height: auto; margin: 1rem auto;" class="mx-auto">
        <p class="text-lg mb-6 text-white">Level Up Your Knowledge: Answer a few questions to unlock your personalised learning path.</p>
        <button id="start-btn" class="bg-red-500 text-white py-3 px-8 rounded-full shadow-md">START</button>
    </div>

    <div id="quiz-container" class="hidden flex flex-col justify-center items-center">
      <div id="progress-bar" class="h-5 w-full bg-gray-300 rounded-lg overflow-hidden mb-4">
          <div id="progress-bar-fill" class="bg-blue-500 h-full" style="width: 0%;"></div>
      </div>
      <p id="message" class="mb-4 text-center"></p>
      <h2 id="question" class="text-lg mb-4 text-center"></h2>
      <div id="options-container" class="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
          <button class="option-btn bg-blue-500 text-white py-2 px-4 rounded-full mb-2" id="btn0"></button>
          <button class="option-btn bg-blue-500 text-white py-2 px-4 rounded-full mb-2" id="btn1"></button>
          <button class="option-btn bg-blue-500 text-white py-2 px-4 rounded-full mb-2" id="btn2"></button>
          <button class="option-btn bg-blue-500 text-white py-2 px-4 rounded-full mb-2" id="btn3"></button>
      </div>
      <button class="skip-btn bg-blue-500 text-white py-2 px-4 rounded-full mb-2" id="skip-btn">I Don't Know</button>
      <button id="next-btn" class="bg-green-500 text-white py-2 px-4 rounded-full mb-2 hidden">Next Question</button>
  </div>


    <div id="failure-container" class="hidden">
      <h2>Thanks for completing the assessment!👏</h2>
      <h2 id="recommendation-text"></h2>
      <h2>We've notified the admins, and they'll be using your results to craft a course recommendation plan just for you. Get ready to learn something new!</h2>


    <div id="feedback-container" class="feedback-container">
        <h3>We value your opinion! Would you like to share your feedback on the assessment?</h3>
        <button id="yes-feedback-btn" class="feedback-btn">Yes, I'll share</button>
        <button id="no-feedback-btn" class="feedback-btn">No, thanks</button>
      </div>

      <div id="feedback-form" class="feedback-form" style="display: none;">
        <h3>We value your feedback!</h3>

        <div class="feedback-question">
          <p>How would you rate the clarity of the questions?</p>
          <div class="rating-container">
            <label><input type="radio" name="clarity" value="Poor"><span>Poor</span></label>
            <label><input type="radio" name="clarity" value="Fair"><span>Fair</span></label>
            <label><input type="radio" name="clarity" value="Good"><span>Good</span></label>
            <label><input type="radio" name="clarity" value="Very Good"><span>Very Good</span></label>
            <label><input type="radio" name="clarity" value="Excellent"><span>Excellent</span></label>
          </div>
        </div>

        <div class="feedback-question">
          <p>How challenging was the assessment?</p>
          <div class="rating-container">
            <label><input type="radio" name="challenge" value="Very Easy"><span>Very Easy</span></label>
            <label><input type="radio" name="challenge" value="Easy"><span>Easy</span></label>
            <label><input type="radio" name="challenge" value="Moderate"><span>Moderate</span></label>
            <label><input type="radio" name="challenge" value="Challenging"><span>Challenging</span></label>
            <label><input type="radio" name="challenge" value="Very Challenging"><span>Very Challenging</span></label>
          </div>
        </div>

        <div class="feedback-question">
          <p>Would you recommend this assessment to others?</p>
          <div class="rating-container">
            <label><input type="radio" name="recommend" value="Definitely Not"><span>Definitely Not</span></label>
            <label><input type="radio" name="recommend" value="Probably Not"><span>Probably Not</span></label>
            <label><input type="radio" name="recommend" value="Maybe"><span>Maybe</span></label>
            <label><input type="radio" name="recommend" value="Probably"><span>Probably</span></label>
            <label><input type="radio" name="recommend" value="Definitely"><span>Definitely</span></label>
          </div>
        </div>

        <button id="submit-feedback" class="submit-feedback">Submit Feedback</button>
      </div>


      <button id="pathway-btn" class="action-button">Skills Gap analysis</button>
  </div>



    <div id="success-container" style="display: none">
        <h2 id="success-heading">You passed Section <span id="current-section">1</span>!</h2>
        <img src="congratulations.gif" alt="Congratulations" class="centered-image">
        <button id="next-section-btn" class="action-button">Proceed to Next Section</button>
      </div>

    <div id="final-success-container" class="hidden text-center">
        <h2 class="text-xl font-bold mb-4">Our sincere congratulations on passing all three levels of the assessment. Your accomplishment demonstrates a high level of competence. We thank you for your participation!</h2>
        <img src="win.gif" alt="Congratulations" class="centered-image mb-6">
        <button class="next-section-btn bg-purple-500 text-white py-2 px-4 rounded-full mb-4" onclick="window.location.href='https://barefootelearning.etraininglibrary.com/'">Return to portal</button>
    </div>

    <div id="pathway-container" class="hidden text-center">
        <h2 id="result-text" class="text-xl font-bold mb-4">Great news!</h2>
        <p class="pathway-info text-lg mb-4">You've completed the learning pathway assessment, and we've created a personalised program designed to help you achieve your learning goals.</p>
        <p class="pathway-info text-lg mb-4">Based on your answers, we suggest the <strong id="suggested-pathway"></strong> Pathway as the most suitable.</p>
        <p class="pathway-info text-lg mb-4">To help you on this journey, we recommend the following courses:</p>
        <ul class="course-list list-none p-0 flex flex-wrap justify-center"></ul>
        <p class="pathway-info text-lg mb-4">We're confident this learning path will equip you with the knowledge and skills you need to be successful.</p>
        <p class="book-text text-lg mb-4">Ready to discuss your learning goals in more detail? We'd be happy to schedule a quick meeting to walk you through the program and answer any questions you might have.</p>
        <button class="book-btn bg-red-500 text-white py-2 px-4 rounded-full" onclick="window.open('https://barefootelearning.com/book-a-call/', '_blank')">Book a Call</button>
    </div>

    <div id="results-summary-container" class="hidden fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white bg-opacity-90 rounded-lg shadow-lg max-w-md w-full p-8 overflow-auto" style="display: none;">
        <div class="summary-header flex justify-between items-center mb-8">
          <h2 class="text-xl font-bold">Results Summary</h2>
          <span class="close-btn text-xl text-gray-400 cursor-pointer">&times;</span>
        </div>
        <div class="summary-content">
          <!-- Content will be dynamically added here -->
        </div>
      </div>

      <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center" style="display: none;">
        <div class="loading-overlay-content">
          <div class="lottie-container">
              <div id="lottie-loading"></div>
          </div>
          <div class="loading-text text-white text-xs"></div>
        </div>
      </div>

      <div id="loading-overlay" class="hidden fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center" style="display: none;">
        <div class="flex flex-col items-center justify-center">
          <div class="lottie-container" style="width: 80px; height: 80px;">
              <div id="lottie-loading"></div>
          </div>
          <div class="loading-text text-white text-sm mt-4"></div>
        </div>
      </div>

      <!-- Loading Overlay -->
<div class="quiz-loading-overlay" id="quiz-loading-overlay">
  <div class="quiz-loading-content">
    <div class="quiz-spinner-container">
      <!-- Circular Progress Bar SVG -->
      <svg class="quiz-circular-progress" width="120" height="120">
        <circle class="quiz-progress-background" cx="60" cy="60" r="54"></circle>
        <circle class="quiz-progress-bar" cx="60" cy="60" r="54"></circle>
      </svg>
      <div class="quiz-progress-percentage">0%</div>
    </div>
    <div class="quiz-loading-text" id="quiz-loading-message">
      Creating your personalised assessment
    </div>
  </div>
</div>

<div class="quiz-loading-overlay" id="modal-loading-overlay">
  <div class="quiz-loading-content">
    <div class="quiz-spinner-container">
      <!-- Circular Progress Bar SVG -->
      <svg class="quiz-circular-progress" width="120" height="120">
        <circle class="quiz-progress-background" cx="60" cy="60" r="54"></circle>
        <circle class="quiz-progress-bar" cx="60" cy="60" r="54"></circle>
      </svg>
      <div class="quiz-progress-percentage">0%</div>
    </div>
    <div class="quiz-loading-text" id="quiz-loading-message">
      Analysing your responses...
    </div>
  </div>
</div>

    <script src="validationFunctions.js"></script>
    <script src="quizFunctions.js"></script>
    <script src="quizlogger.js"></script>
    <script src="script2.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    <script src="sgaframework.js"></script>
    <script src="skills-gap-modal.js"></script>
</body>

</html>
